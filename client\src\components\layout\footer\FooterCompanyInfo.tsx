import { useLanguage } from '@/hooks/use-language';
import { Gem } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FaFacebook, FaInstagram, FaLinkedin, FaYoutube } from 'react-icons/fa';
import { IconType } from 'react-icons';

interface SocialLink {
  icon: IconType;
  href: string;
  label: string;
}

export function FooterCompanyInfo() {
  const { t } = useLanguage();

  const socialLinks: SocialLink[] = [
    { icon: FaFacebook, href: '#', label: 'Facebook' },
    { icon: FaInstagram, href: '#', label: 'Instagram' },
    { icon: FaLinkedin, href: '#', label: 'LinkedIn' },
    { icon: FaYoutube, href: '#', label: 'YouTube' },
  ];

  return (
    <div className="">
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-12 h-12 gold-gradient rounded-lg flex items-center justify-center">
          <Gem className="text-white text-xl" />
        </div>
        <div>
          <h3 className="font-playfair font-bold text-xl">{t('footer.companyName')}</h3>
          <p className="text-gray-300 text-sm">{t('footer.companyTagline')}</p>
        </div>
      </div>
      <p className="text-gray-300 leading-relaxed mb-6 max-w-md">{t('footer.description')}</p>
      <div className="flex space-x-4">
        {socialLinks.map(social => {
          const IconComponent = social.icon;
          return (
            <Button
              key={social.label}
              variant="ghost"
              size="sm"
              className="w-10 h-10 bg-gray-700 rounded-lg hover:bg-gold transition-colors duration-300"
              asChild>
              <a href={social.href} target="_blank" rel="noopener noreferrer" aria-label={social.label}>
                <IconComponent className="w-6 h-6 text-white" />
              </a>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
