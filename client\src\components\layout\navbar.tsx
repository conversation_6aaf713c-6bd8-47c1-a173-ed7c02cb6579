import { useState, useEffect } from 'react';
import { Link, useLocation } from 'wouter';
import { useLanguage } from '@/hooks/use-language';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, Gem, Shield } from 'lucide-react';
import { cn, rtlSpace, rtlMargin } from '@/lib/utils';

export function Navbar() {
  const [location] = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { t, isRTL } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { href: '/', label: t('nav.home') },
    { href: '/products', label: t('nav.products') },
    { href: '/categories', label: t('nav.categories') },
    { href: '/about', label: t('nav.about') },
    { href: '/contact', label: t('nav.contact') },
  ];

  return (
    <nav
      className={`fixed w-full z-50 top-0 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-lg' : 'glass-effect'
      }`}>
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link href="/" className={cn('flex items-center', rtlSpace(isRTL, 'space-x-4'))}>
            <div className="w-12 h-12 gold-gradient rounded-lg flex items-center justify-center">
              <Gem className="text-white text-xl" />
            </div>
            <div>
              <h1 className="font-playfair font-bold text-xl text-charcoal">{isRTL ? 'حجر فاخر' : 'Horizon'}</h1>
              <p className="text-xs text-medium-gray font-light">{t('footer.companyTagline')}</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className={cn('hidden lg:flex items-center', rtlSpace(isRTL, 'space-x-8'))}>
            {navItems.map(item => (
              <Link
                key={item.href}
                href={item.href}
                className={`transition-colors duration-300 font-medium ${
                  location === item.href ? 'text-gold' : 'text-charcoal hover:text-gold'
                }`}>
                {item.label}
              </Link>
            ))}
          </div>

          {/* Right Side Controls */}
          <div className={cn('flex items-center', rtlSpace(isRTL, 'space-x-4'))}>
            <LanguageSwitcher />

            {/* Admin Button */}
            <Link href="/admin" className="hidden lg:block">
              <Button
                variant="default"
                size="sm"
                className="bg-charcoal text-white hover:bg-gold transition-all duration-300">
                <Shield className={cn('h-4 w-4', rtlMargin(isRTL, 'mr-2', 'ml-2'))} />
                {t('nav.admin')}
              </Button>
            </Link>

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="lg:hidden text-charcoal hover:text-gold transition-colors duration-300">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side={isRTL ? 'left' : 'right'} className="w-80">
                <div className="flex flex-col space-y-4 mt-8">
                  {navItems.map(item => (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`text-lg transition-colors duration-300 font-medium ${
                        location === item.href ? 'text-gold' : 'text-charcoal hover:text-gold'
                      }`}>
                      {item.label}
                    </Link>
                  ))}
                  <Link href="/admin" onClick={() => setIsMobileMenuOpen(false)}>
                    <Button
                      variant="default"
                      className="w-full bg-charcoal text-white hover:bg-gold transition-all duration-300">
                      <Shield className={cn('h-4 w-4', rtlMargin(isRTL, 'mr-2', 'ml-2'))} />
                      {t('nav.admin')}
                    </Button>
                  </Link>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
